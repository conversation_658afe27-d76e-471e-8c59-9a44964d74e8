"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Upload, Mail, FileSpreadsheet, Eye, EyeOff, Plus } from "lucide-react"
import { Switch } from "@/components/ui/switch"
import type { Role } from "@/types/team"
import { AddRoleDialog } from "./add-role-dialog"
import { useAuth } from "@/lib/api/auth"

interface BulkInviteDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  roles: Role[]
  onBulkInvite: (emails: string[], role: string, password?: string) => void
  teamId?: string
  onRoleAdded?: (role: Role) => void
}

export function BulkInviteDialog({ open, onOpenChange, roles, onBulkInvite, teamId, onRoleAdded }: BulkInviteDialogProps) {
  const [emails, setEmails] = useState<string>("")
  const [csvFile, setCsvFile] = useState<File | null>(null)
  const [selectedRole, setSelectedRole] = useState<string>("")
  const [activeTab, setActiveTab] = useState<string>("manual")
  const [autoGeneratePassword, setAutoGeneratePassword] = useState(true)
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [showAddRoleDialog, setShowAddRoleDialog] = useState(false)
  const { user } = useAuth()

  // Kullanıcının rol oluşturma yetkisi var mı?
  const canManageRoles = user?.accountType === 'company_owner' || user?.teamRole === 'team_admin'

  const handleSubmit = async () => {
    let emailList: string[] = []

    if (activeTab === "manual") {
      // Process manually entered emails
      emailList = emails
        .split(/[\n,;]/)
        .map((email) => email.trim())
        .filter((email) => email.includes("@"))
    } else if (activeTab === "csv" && csvFile) {
      // Process CSV file
      try {
        const text = await csvFile.text()
        const lines = text.split("\n")
        emailList = lines
          .map((line) => {
            const parts = line.split(",")
            return parts[0].trim() // Assuming first column is email
          })
          .filter((email) => email.includes("@"))
      } catch (error) {
        console.error("CSV dosyası işlenirken hata oluştu:", error)
      }
    }

    if (emailList.length > 0 && selectedRole) {
      onBulkInvite(emailList, selectedRole, autoGeneratePassword ? undefined : password)
      setEmails("")
      setCsvFile(null)
      setSelectedRole("")
      setPassword("")
      setAutoGeneratePassword(true)
      onOpenChange(false)
    }
  }

  const handleAddRole = (newRole: Role) => {
    if (onRoleAdded) {
      onRoleAdded(newRole)
    }
    // Yeni oluşturulan rolü otomatik olarak seç
    setSelectedRole(newRole.id)
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setCsvFile(e.target.files[0])
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Toplu Davet Gönder</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="manual" className="w-full" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="manual" className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              E-posta Giriş
            </TabsTrigger>
            <TabsTrigger value="csv" className="flex items-center gap-2">
              <FileSpreadsheet className="h-4 w-4" />
              CSV Yükleme
            </TabsTrigger>
          </TabsList>

          <TabsContent value="manual" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="emails">E-posta Adresleri</Label>
              <Textarea
                id="emails"
                placeholder="Her satıra bir e-posta adresi girin veya virgülle ayırın"
                value={emails}
                onChange={(e) => setEmails(e.target.value)}
                className="min-h-[120px]"
              />
              <p className="text-xs text-gray-500">
                Örnek: <EMAIL>, <EMAIL> veya her satıra bir e-posta
              </p>
            </div>
          </TabsContent>

          <TabsContent value="csv" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="csv-file">CSV Dosyası Yükle</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-md p-6 flex flex-col items-center justify-center">
                <Upload className="h-8 w-8 text-gray-400 mb-2" />
                <p className="text-sm text-gray-500 mb-2">CSV dosyasını sürükleyin veya seçin</p>
                <input id="csv-file" type="file" accept=".csv" onChange={handleFileChange} className="hidden" />
                <Button variant="outline" size="sm" onClick={() => document.getElementById("csv-file")?.click()}>
                  Dosya Seç
                </Button>
                {csvFile && <p className="text-sm text-green-600 mt-2">Seçilen dosya: {csvFile.name}</p>}
              </div>
              <p className="text-xs text-gray-500">İlk sütunda e-posta adresleri olan bir CSV dosyası yükleyin</p>
            </div>
          </TabsContent>
        </Tabs>

        <div className="grid gap-4">
          <div className="space-y-2">
            <Label htmlFor="role">Rol Seçin</Label>
            <Select value={selectedRole} onValueChange={(value) => {
              if (value === "__add_new_role__") {
                setShowAddRoleDialog(true)
              } else {
                setSelectedRole(value)
              }
            }}>
              <SelectTrigger id="role">
                <SelectValue placeholder="Bir rol seçin" />
              </SelectTrigger>
              <SelectContent>
                {roles.map((role) => (
                  <SelectItem key={role.id} value={role.name}>
                    {role.name}
                  </SelectItem>
                ))}
                {canManageRoles && teamId && (
                  <SelectItem value="__add_new_role__">
                    <div className="flex items-center gap-2 text-blue-600">
                      <Plus className="h-4 w-4" />
                      Yeni Rol Oluştur
                    </div>
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="auto-generate" className="text-right">
              Şifre Belirleme
            </Label>
            <div className="flex items-center space-x-2 col-span-3">
              <Switch id="auto-generate" checked={autoGeneratePassword} onCheckedChange={setAutoGeneratePassword} />
              <Label htmlFor="auto-generate">Otomatik şifre oluştur</Label>
            </div>
          </div>

          {!autoGeneratePassword && (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="password" className="text-right">
                Şifre
              </Label>
              <div className="col-span-3 relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pr-10"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>
          )}

          {autoGeneratePassword && (
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="col-span-4 bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Güvenli bir şifre otomatik olarak oluşturulacak ve kullanıcıya e-posta ile gönderilecektir.
                </p>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            İptal
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={
              !selectedRole || 
              (activeTab === "manual" && !emails) || 
              (activeTab === "csv" && !csvFile) ||
              (!autoGeneratePassword && !password)
            }
          >
            Davet Gönder
          </Button>
        </DialogFooter>
      </DialogContent>

      {/* Add Role Dialog */}
      <AddRoleDialog
        open={showAddRoleDialog}
        onOpenChange={setShowAddRoleDialog}
        onAddRole={handleAddRole}
        teamId={teamId}
      />
    </Dialog>
  )
}
