"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alogFooter } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Eye, EyeOff, RefreshCw, Plus } from "lucide-react"
import type { TeamMember, Role } from "@/types/team"
import { AddRoleDialog } from "./add-role-dialog"
import { useAuth } from "@/lib/api/auth"

interface AddMemberDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  roles: Role[]
  onAddMember: (member: TeamMember, password?: string) => void
  teamId?: string
  onRoleAdded?: (role: Role) => void
}

export function AddMemberDialog({ open, onOpenChange, roles, onAddMember, teamId, onRoleAdded }: AddMemberDialogProps) {
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [role, setRole] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [showAddRoleDialog, setShowAddRoleDialog] = useState(false)
  const { user } = useAuth()

  // Kullanıcının rol oluşturma yetkisi var mı?
  const canManageRoles = user?.accountType === 'company_owner' || user?.teamRole === 'team_admin'

  // Debug
  console.log('AddMemberDialog Debug:', {
    user,
    teamId,
    canManageRoles,
    accountType: user?.accountType,
    teamRole: user?.teamRole
  })

  // Debug: roles array'ini log'la
  console.log('AddMemberDialog roles:', roles)

  const generateRandomPassword = () => {
    const lowercase = "abcdefghijklmnopqrstuvwxyz"
    const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    const numbers = "**********"
    const symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?"
    const allChars = lowercase + uppercase + numbers + symbols

    let newPassword = ""
    // Ensure at least one character from each category
    newPassword += lowercase[Math.floor(Math.random() * lowercase.length)]
    newPassword += uppercase[Math.floor(Math.random() * uppercase.length)]
    newPassword += numbers[Math.floor(Math.random() * numbers.length)]
    newPassword += symbols[Math.floor(Math.random() * symbols.length)]

    // Add more random characters to reach desired length (12)
    for (let i = 0; i < 8; i++) {
      newPassword += allChars[Math.floor(Math.random() * allChars.length)]
    }

    // Shuffle the password
    return newPassword
      .split("")
      .sort(() => 0.5 - Math.random())
      .join("")
  }

  // Şifre oluşturucu
  const handleGeneratePassword = () => {
    const newPassword = generateRandomPassword()
    setPassword(newPassword)
    setShowPassword(true) // Oluşturulduktan sonra şifreyi göster
  }

  const handleSubmit = () => {
    if (name && email && password && role) {
      const newMember: TeamMember = {
        id: Math.random().toString(36).substring(2, 11),
        name,
        email,
        role,
        status: "active",
        createdAt: new Date().toISOString(),
      }

      onAddMember(newMember, password)
      resetForm()
      onOpenChange(false)
    }
  }

  const resetForm = () => {
    setName("")
    setEmail("")
    setPassword("")
    setRole("")
    setShowPassword(false)
  }

  const handleAddRole = (newRole: Role) => {
    if (onRoleAdded) {
      onRoleAdded(newRole)
    }
    // Yeni oluşturulan rolü otomatik olarak seç
    setRole(newRole.id)
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) resetForm()
        onOpenChange(isOpen)
      }}
    >
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Yeni Takım Üyesi Ekle</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Ad Soyad
            </Label>
            <Input id="name" value={name} onChange={(e) => setName(e.target.value)} className="col-span-3" />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="email" className="text-right">
              E-posta
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="col-span-3"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="password" className="text-right">
              Şifre
            </Label>
            <div className="col-span-3 relative">
              <div className="flex space-x-2">
                <div className="relative flex-1">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={handleGeneratePassword}
                  title="Şifre oluştur"
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
              {password && (
                <p className="text-xs text-gray-500 mt-1">
                  Şifre: <span className="font-mono bg-gray-100 dark:bg-gray-800 p-1 rounded">{password}</span>
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="role" className="text-right">
              Rol
            </Label>
            <div className="col-span-3 space-y-2">
              <Select value={role} onValueChange={(value) => {
                if (value === "__add_new_role__") {
                  setShowAddRoleDialog(true)
                } else {
                  setRole(value)
                }
              }}>
                <SelectTrigger id="role">
                  <SelectValue placeholder="Bir rol seçin" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.name}
                      {role.description && (
                        <span className="text-xs text-gray-500 ml-2">
                          - {role.description}
                        </span>
                      )}
                    </SelectItem>
                  ))}
                  {canManageRoles && teamId && (
                    <SelectItem value="__add_new_role__">
                      <div className="flex items-center gap-2 text-blue-600">
                        <Plus className="h-4 w-4" />
                        Yeni Rol Oluştur
                      </div>
                    </SelectItem>
                  )}
                  {/* Debug için */}
                  {console.log('Render check:', { canManageRoles, teamId, shouldShow: canManageRoles && teamId })}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => {
              resetForm()
              onOpenChange(false)
            }}
          >
            İptal
          </Button>
          <Button onClick={handleSubmit} disabled={!name || !email || !password || !role}>
            Ekle
          </Button>
        </DialogFooter>
      </DialogContent>

      {/* Add Role Dialog */}
      <AddRoleDialog
        open={showAddRoleDialog}
        onOpenChange={setShowAddRoleDialog}
        onAddRole={handleAddRole}
        teamId={teamId}
      />
    </Dialog>
  )
}
