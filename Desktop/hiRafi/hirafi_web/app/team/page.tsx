"use client"

import { useState, use<PERSON>ffect, use<PERSON><PERSON><PERSON>, use<PERSON>em<PERSON> } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Sidebar } from "@/components/sidebar/sidebar"
import { TeamMembers } from "@/components/team/team-members"
import { RoleManagement } from "@/components/team/role-management"
import { OrganizationChart } from "@/components/team/organization-chart"
import { TeamStats } from "@/components/team/team-stats"
import { PlusCircle, Users, ShieldCheck, GitBranchPlus } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useToast } from "@/components/ui/use-toast"
import type { TeamMember, Role } from "@/types/team"
import { AddMemberDialog } from "@/components/team/add-member-dialog"
import { BulkInviteDialog } from "@/components/team/bulk-invite-dialog"
import { BulkInviteResults } from "@/components/team/bulk-invite-results"
import { useAuth } from "@/lib/api/auth"
import { useParams } from "next/navigation"
import { ProtectedRoute } from "@/components/protected-route"
import { PermissionGuard } from "@/components/permission-guard"
import { teamApi } from "@/lib/api"

// Fallback mock data (sadece yükleme hataları durumunda kullanılacak)
import { mockTeamMembers, mockRoles } from "@/components/team/mock-data"

function TeamContent() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showBulkInviteDialog, setShowBulkInviteDialog] = useState(false)
  const [showResultsDialog, setShowResultsDialog] = useState(false)
  const [inviteResults, setInviteResults] = useState<Array<{
    email: string;
    success: boolean;
    message?: string;
    inviteId?: string;
  }>>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()
  const params = useParams()
  const { user } = useAuth()

  // Kullanıcının company_owner olup olmadığını kontrol et
  const isCompanyOwner = user?.accountType === 'company_owner'

  // Compute the effective team ID only when its dependencies change
  const effectiveTeamId = useMemo(() => {
    const paramTeamId = params?.teamId as string
    const userTeamId = user?.teamId

    // URL parameter takes precedence, then user's teamId
    return paramTeamId || userTeamId || null
  }, [params?.teamId, user?.teamId])

  // Store the current team ID
  const [currentTeamId, setCurrentTeamId] = useState<string | null>(null)

  // Fetch team members - memoize to prevent recreation on every render
  const fetchTeamMembers = useCallback(async (teamId: string) => {
    try {
      const response = await teamApi.getTeamMembers(teamId);

      if (!response.success) {
        setError(`Takım üyeleri yüklenemedi: ${response.error}`)
        setTeamMembers(mockTeamMembers)
        return null
      }

      // Kullanıcı adlarını kontrol et ve düzelt
      const processedMembers = response.data.members.map(member => {
        // Kullanıcı adı boş veya rol adı ile aynıysa, user nesnesinden al
        if ((!member.name || member.name === member.role_name) && member.user && member.user.name) {
          return { ...member, name: member.user.name }
        }
        return member
      })

      return processedMembers
    } catch (error: any) {
      setError("Takım üyeleri verisi ayrıştırılamadı")
      setTeamMembers(mockTeamMembers)
      return null
    }
  }, [])

  // Fetch team roles - memoize to prevent recreation on every render
  const fetchTeamRoles = useCallback(async (teamId: string) => {
    try {
      const response = await teamApi.getTeamRoles(teamId);

      if (!response.success) {
        setError(`Roller yüklenemedi: ${response.error}`)
        setRoles(mockRoles)
        return null
      }

      return response.data.roles
    } catch (error: any) {
      setError("Roller verisi ayrıştırılamadı")
      setRoles(mockRoles)
      return null
    }
  }, [])

  // Refresh team data - this can be called when data needs to be refreshed
  const refreshTeamData = useCallback(async (teamId: string) => {
    if (!teamId) return

    setIsLoading(true)
    setError(null)

    try {
      // Fetch team members and roles in parallel for efficiency
      const [membersData, rolesData] = await Promise.all([
        fetchTeamMembers(teamId),
        fetchTeamRoles(teamId)
      ])

      // Only update state if data was successfully fetched
      if (membersData) setTeamMembers(membersData)
      if (rolesData) setRoles(rolesData)
    } catch (err) {
      setError("Takım verileri yüklenemedi")
      setTeamMembers(mockTeamMembers)
      setRoles(mockRoles)
    } finally {
      setIsLoading(false)
    }
  }, [fetchTeamMembers, fetchTeamRoles])

  // Determine and setup the team ID when auth/params change
  useEffect(() => {
    const setupTeamId = async () => {
      setIsLoading(true)
      setError(null)

      try {
        // If we already have a team ID from URL or user context, use it
        if (effectiveTeamId) {
          // Team ID kullanılıyor

          // Only update if the team ID has changed to prevent unnecessary renders
          if (currentTeamId !== effectiveTeamId) {
            setCurrentTeamId(effectiveTeamId)
          }
          return
        }

        // Otherwise fetch teams to find a default team ID
        // Takım ID'si bulunamadı, API'den takımları çekmeye çalışılıyor
        const response = await teamApi.getTeams();

        if (!response.success) {
          setError(`Takımlar yüklenemedi: ${response.error}`)
          setIsLoading(false)
          return
        }

        if (!response.data.teams || response.data.teams.length === 0) {
          // API'den hiç takım bulunamadı
          setError("Kullanıcının hiç takımı bulunamadı")
          setIsLoading(false)
          return
        }

        // Use the first team ID
        const firstTeamId = response.data.teams[0].id
        // İlk takım ID'si kullanılıyor

        // Only update if the team ID has changed
        if (currentTeamId !== firstTeamId) {
          setCurrentTeamId(firstTeamId)
        }
      } catch (err) {
        setError("Takım ID'si belirlenemedi")
        setIsLoading(false)
      }
    }

    setupTeamId()
  }, [effectiveTeamId, currentTeamId]) // Only depends on effectiveTeamId which is already memoized

  // Load team data when the currentTeamId changes
  useEffect(() => {
    if (currentTeamId) {
      refreshTeamData(currentTeamId)
    }
  }, [currentTeamId, refreshTeamData])

  // Handler functions - use useCallback to prevent unnecessary recreations
  const handleAddMember = useCallback(async (member: TeamMember, password?: string) => {
    if (!currentTeamId) {
      toast({
        title: "Hata",
        description: "Takım ID'si bulunamadı, lütfen sayfayı yenileyin",
        variant: "destructive"
      })
      return
    }

    try {
      const response = await teamApi.bulkInviteTeamMembers(currentTeamId, {
        emails: [member.email],
        roleId: member.role,
        password
      });

      if (!response.success) {
        toast({
          title: "Hata",
          description: response.error || "Üye eklenirken bir hata oluştu.",
          variant: "destructive"
        })
        return
      }

      const data = response.data

      if (data.success) {
        // Refresh member data
        refreshTeamData(currentTeamId)

        // Set results and show results dialog
        setInviteResults(data.results || [])
        setShowResultsDialog(true)

        // Eğer manuel şifre belirlenmiş ise, bunu toast ile göster
        if (password) {
          toast({
            title: "Bilgi",
            description: `Kullanıcı için belirlenen şifre: ${password}`,
            variant: "default"
          })
        }
      } else {
        toast({
          title: "Hata",
          description: data.error || "Üye eklenirken bir hata oluştu.",
          variant: "destructive"
        })
      }
    } catch (err: any) {
      toast({
        title: "Hata",
        description: err.message || "Üye eklenirken bir hata oluştu.",
        variant: "destructive"
      })
    }
  }, [currentTeamId, toast, refreshTeamData])

  const handleUpdateMember = useCallback(async (member: TeamMember) => {
    if (!currentTeamId) return

    try {
      // Kullanıcı ID'sini doğru şekilde al
      const userId = member.userId || member.user_id || member.user?.id || member.id;
      // Üye güncelleniyor

      const response = await teamApi.updateTeamMember(currentTeamId, userId, {
        roleId: member.role,
        status: member.status
      });

      if (response.success) {
        // Optimistik olarak UI'ı güncelle
        setTeamMembers(prevMembers =>
          prevMembers.map((m) => (m.id === member.id ? member : m))
        )

        toast({
          title: "Takım üyesi güncellendi",
          description: `${member.name} bilgileri güncellendi.`,
        })
      } else {
        toast({
          title: "Hata",
          description: response.error || "Üye güncellenirken bir hata oluştu.",
          variant: "destructive"
        })
      }
    } catch (err: any) {
      toast({
        title: "Hata",
        description: err.message || "Üye güncellenirken bir hata oluştu.",
        variant: "destructive"
      })
    }
  }, [currentTeamId, toast])

  const handleDeleteMember = useCallback(async (id: string) => {
    if (!currentTeamId) return

    const memberToDelete = teamMembers.find((m) => m.id === id)
    if (!memberToDelete) return

    try {
      // Kullanıcı ID'sini doğru şekilde al
      const userId = memberToDelete.userId || memberToDelete.user_id || memberToDelete.user?.id || id;
      // Üye siliniyor

      const response = await teamApi.removeTeamMember(currentTeamId, userId);

      if (response.success) {
        // Optimistik olarak UI'ı güncelle
        setTeamMembers(prevMembers =>
          prevMembers.filter((m) => m.id !== id)
        )

        toast({
          title: "Takım üyesi silindi",
          description: `${memberToDelete.name} takımdan çıkarıldı.`,
        })
      } else {
        toast({
          title: "Hata",
          description: response.error || "Üye silinirken bir hata oluştu.",
          variant: "destructive"
        })
      }
    } catch (err: any) {
      toast({
        title: "Hata",
        description: err.message || "Üye silinirken bir hata oluştu.",
        variant: "destructive"
      })
    }
  }, [currentTeamId, teamMembers, toast])

  const handleDeactivateMember = useCallback(async (id: string) => {
    if (!currentTeamId) return

    const memberToUpdate = teamMembers.find((m) => m.id === id)
    if (!memberToUpdate) return

    try {
      // Kullanıcı ID'sini doğru şekilde al
      const userId = memberToUpdate.userId || memberToUpdate.user_id || memberToUpdate.user?.id || id;

      const response = await teamApi.updateTeamMemberStatus(currentTeamId, userId, "inactive");

      if (response.success) {
        // Optimistik olarak UI'ı güncelle
        const updatedMember = { ...memberToUpdate, status: "inactive" as const }
        setTeamMembers(prevMembers =>
          prevMembers.map((m) => (m.id === id ? updatedMember : m))
        )

        toast({
          title: "Takım üyesi deaktif edildi",
          description: `${memberToUpdate.name} deaktif edildi.`,
        })
      } else {
        toast({
          title: "Hata",
          description: response.error || "Üye durumu güncellenirken bir hata oluştu.",
          variant: "destructive"
        })
      }
    } catch (err: any) {
      toast({
        title: "Hata",
        description: err.message || "Üye durumu güncellenirken bir hata oluştu.",
        variant: "destructive"
      })
    }
  }, [currentTeamId, teamMembers, toast])

  const handleActivateMember = useCallback(async (id: string) => {
    if (!currentTeamId) return

    const memberToUpdate = teamMembers.find((m) => m.id === id)
    if (!memberToUpdate) return

    try {
      // Kullanıcı ID'sini doğru şekilde al
      const userId = memberToUpdate.userId || memberToUpdate.user_id || memberToUpdate.user?.id || id;

      const response = await teamApi.updateTeamMemberStatus(currentTeamId, userId, "active");

      if (response.success) {
        // Optimistik olarak UI'ı güncelle
        const updatedMember = { ...memberToUpdate, status: "active" as const }
        setTeamMembers(prevMembers =>
          prevMembers.map((m) => (m.id === id ? updatedMember : m))
        )

        toast({
          title: "Takım üyesi aktif edildi",
          description: `${memberToUpdate.name} aktif edildi.`,
        })
      } else {
        toast({
          title: "Hata",
          description: response.error || "Üye durumu güncellenirken bir hata oluştu.",
          variant: "destructive"
        })
      }
    } catch (err: any) {
      toast({
        title: "Hata",
        description: err.message || "Üye durumu güncellenirken bir hata oluştu.",
        variant: "destructive"
      })
    }
  }, [currentTeamId, teamMembers, toast])

  const handleAddRole = useCallback(async (role: Role) => {
    if (!currentTeamId) return

    try {
      const response = await teamApi.createTeamRole(currentTeamId, role);

      if (response.success) {
        // Refresh role data
        refreshTeamData(currentTeamId)

        toast({
          title: "Rol eklendi",
          description: `${role.name} rolü başarıyla eklendi.`,
        })
      } else {
        toast({
          title: "Hata",
          description: response.error || "Rol eklenirken bir hata oluştu.",
          variant: "destructive"
        })
      }
    } catch (err: any) {
      toast({
        title: "Hata",
        description: err.message || "Rol eklenirken bir hata oluştu.",
        variant: "destructive"
      })
    }
  }, [currentTeamId, toast, refreshTeamData])

  const handleUpdateRole = useCallback(async (role: Role) => {
    if (!currentTeamId) return

    try {
      const response = await teamApi.updateTeamRole(currentTeamId, role.id, role);

      if (response.success) {
        // Optimistik olarak UI'ı güncelle
        setRoles(prevRoles =>
          prevRoles.map((r) => (r.id === role.id ? role : r))
        )

        toast({
          title: "Rol güncellendi",
          description: `${role.name} rolü başarıyla güncellendi.`,
        })
      } else {
        toast({
          title: "Hata",
          description: response.error || "Rol güncellenirken bir hata oluştu.",
          variant: "destructive"
        })
      }
    } catch (err: any) {
      toast({
        title: "Hata",
        description: err.message || "Rol güncellenirken bir hata oluştu.",
        variant: "destructive"
      })
    }
  }, [currentTeamId, toast])

  const handleDeleteRole = useCallback(async (id: string) => {
    if (!currentTeamId) return

    const roleToDelete = roles.find((r) => r.id === id)
    if (!roleToDelete) return

    try {
      const response = await teamApi.deleteTeamRole(currentTeamId, id);

      if (response.success) {
        // Optimistik olarak UI'ı güncelle
        setRoles(prevRoles =>
          prevRoles.filter((r) => r.id !== id)
        )

        toast({
          title: "Rol silindi",
          description: `${roleToDelete.name} rolü başarıyla silindi.`,
        })
      } else {
        toast({
          title: "Hata",
          description: response.error || "Rol silinirken bir hata oluştu.",
          variant: "destructive"
        })
      }
    } catch (err: any) {
      toast({
        title: "Hata",
        description: err.message || "Rol silinirken bir hata oluştu.",
        variant: "destructive"
      })
    }
  }, [currentTeamId, roles, toast])

  const handleBulkInvite = useCallback(async (emails: string[], role: string, password?: string) => {
    if (!currentTeamId) return

    try {
      // Toplu davet gönderme isteği
      const response = await teamApi.bulkInviteTeamMembers(currentTeamId, {
        emails,
        roleId: role,
        password
      });

      if (!response.success) {
        toast({
          title: "Hata",
          description: response.error || "Davetler gönderilirken bir hata oluştu",
          variant: "destructive"
        })
        return
      }

      // Refresh member data
      refreshTeamData(currentTeamId)

      // Sonuçları göster
      setInviteResults(response.data.results || [])
      setShowResultsDialog(true)

      // Eğer manuel şifre belirlenmiş ise, bunu toast ile göster
      if (password) {
        toast({
          title: "Bilgi",
          description: `Kullanıcılar için belirlenen şifre: ${password}`,
          variant: "default"
        })
      }
    } catch (err) {
      toast({
        title: "Hata",
        description: "Davetler gönderilirken bir hata oluştu",
        variant: "destructive"
      })
    }
  }, [currentTeamId, toast, refreshTeamData])

  // Yükleme durumunu göster
  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center bg-white dark:bg-gray-950">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-500 dark:text-gray-400">Takım verileri yükleniyor...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-white dark:bg-gray-950">
      {/* Navigation Sidebar */}
      <Sidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <header className="border-b border-gray-200 dark:border-gray-800 p-4 bg-white dark:bg-gray-900">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Takım Yönetimi</h1>
              <p className="text-gray-500 dark:text-gray-400">Takım üyelerinizi ve rollerinizi yönetin</p>
              {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
            </div>
            <div className="flex space-x-2">
              {/* Sadece company owner kullanıcıları üye ekleyebilir */}
              {isCompanyOwner && (
                <>
                  <Button
                    variant="outline"
                    className="flex items-center gap-2"
                    onClick={() => setShowBulkInviteDialog(true)}
                  >
                    <GitBranchPlus className="h-4 w-4" />
                    Toplu Davet
                  </Button>
                  <Button
                    className="flex items-center gap-2 bg-gradient-to-r from-indigo-600 to-violet-600 hover:from-indigo-700 hover:to-violet-700"
                    onClick={() => setShowAddDialog(true)}
                  >
                    <PlusCircle className="h-4 w-4" />
                    Yeni Üye Ekle
                  </Button>
                </>
              )}
            </div>
          </div>
        </header>

        <main className="flex-1 overflow-auto p-6 bg-gray-50 dark:bg-gray-950">
          <div className="max-w-7xl mx-auto">
            {/* Team Stats */}
            <TeamStats teamMembers={teamMembers} roles={roles} />

            <Tabs defaultValue="members" className="w-full mt-6">
              <TabsList className="grid grid-cols-3 mb-8 w-[400px]">
                <TabsTrigger value="members" className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Üyeler
                </TabsTrigger>
                <TabsTrigger value="roles" className="flex items-center gap-2">
                  <ShieldCheck className="h-4 w-4" />
                  Roller
                </TabsTrigger>
                <TabsTrigger value="chart" className="flex items-center gap-2">
                  <GitBranchPlus className="h-4 w-4" />
                  Organizasyon
                </TabsTrigger>
              </TabsList>

              <TabsContent value="members" className="space-y-4">
                <TeamMembers
                  members={teamMembers}
                  roles={roles}
                  onAddMember={handleAddMember}
                  onUpdateMember={handleUpdateMember}
                  onDeleteMember={handleDeleteMember}
                  onDeactivateMember={handleDeactivateMember}
                  onActivateMember={handleActivateMember}
                />
              </TabsContent>

              <TabsContent value="roles" className="space-y-4">
                <RoleManagement
                  roles={roles}
                  onAddRole={handleAddRole}
                  onUpdateRole={handleUpdateRole}
                  onDeleteRole={handleDeleteRole}
                  teamId={currentTeamId || undefined}
                />
              </TabsContent>

              <TabsContent value="chart" className="space-y-4">
                <OrganizationChart members={teamMembers} roles={roles} />
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>

      {/* Add Member Dialog */}
      <AddMemberDialog
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
        roles={roles}
        onAddMember={handleAddMember}
      />

      {/* Bulk Invite Dialog */}
      <BulkInviteDialog
        open={showBulkInviteDialog}
        onOpenChange={setShowBulkInviteDialog}
        roles={roles}
        onBulkInvite={handleBulkInvite}
      />

      {/* Add new Results Dialog */}
      <BulkInviteResults
        open={showResultsDialog}
        onOpenChange={setShowResultsDialog}
        results={inviteResults}
      />
    </div>
  )
}

export default function TeamPage() {
  return (
    <ProtectedRoute>
      <PermissionGuard resource="Team" action="view">
        <TeamContent />
      </PermissionGuard>
    </ProtectedRoute>
  )
}
