/**
 * Team Routes
 * Takım yönetimiyle ilgili API rotaları
 */

import express, { Response, NextFunction } from 'express';
import { authenticate as requireAuth, AuthRequest } from '../middleware/authMiddleware.js';
import { logger } from '../../utils/logger.js';
import {
  createTeam,
  getTeamById,
  getTeamsByUserId,
  updateTeam,
  deleteTeam,
  addTeamMember,
  updateTeamMember,
  removeTeamMember,
  getTeamMembers,
  authorizeTeamAction,
  checkTeamMembership
} from '../../services/mongo/teamService.js';
import { MembershipStatus } from '../../models/team.js';
import {
  getRolesByTeamId
} from '../../services/mongo/roleService.js';
import { inviteUserToTeam } from '../../services/mongo/userService.js';
import { isMongoDBInitialized, usersCollection, db } from '../../services/mongo/dbConnection.js';

// Permission kontrolü için utility fonksiyon
function hasPermission(userPermissions: any[], resource: string, action: string): boolean {
  return userPermissions.some((permission: any) => {
    // Exact match
    if (permission.resource === resource && permission.action === action) {
      return true;
    }

    // Wildcard action
    if (permission.resource === resource && permission.action === '*') {
      return true;
    }

    // Manage action (manage tüm action'lara izin verir)
    if (permission.resource === resource && permission.action === 'manage') {
      return true;
    }

    // Wildcard resource (admin için)
    if (permission.resource === '*' && (permission.action === 'manage' || permission.action === '*')) {
      return true;
    }

    return false;
  });
}

const router = express.Router();

// Tüm takım rotaları için yetkilendirme
router.use(requireAuth);

// Takım üyeliği kontrolü için middleware
const checkTeamAccess = async (req: AuthRequest, res: Response, next: NextFunction) => {
  try {
    const { teamId } = req.params;
    const userId = req.user?.id;
    const accountType = req.user?.accountType;

    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    if (!teamId) {
      return res.status(400).json({ error: 'Team ID is required' });
    }

    // Permission-based kontrol: Team.view permission'ı gerekli
    const userPermissions = req.user?.permissions || [];
    const hasTeamViewPermission = hasPermission(userPermissions, 'Team', 'view');

    if (hasTeamViewPermission) {
      logger.info(`User ${userId} has Team.view permission and can access team ${teamId}`);
      return next();
    }

    // Kullanıcının takım üyeliğini kontrol et
    const membershipCheck = await checkTeamMembership(userId, teamId);

    if (!membershipCheck.success) {
      logger.error(`Membership check error: ${membershipCheck.message}`);
      return res.status(500).json({ error: 'Failed to verify team membership' });
    }

    if (!membershipCheck.isMember) {
      logger.warn(`User ${userId} attempted to access team ${teamId} but is not a member`);
      return res.status(403).json({ error: 'You are not a member of this team' });
    }

    next();
  } catch (error: any) {
    logger.error(`Error in team access check: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

// Takım üyeliği gerektiren rotalar için middleware'i ekle
router.use('/:teamId', checkTeamAccess);

/**
 * @route   POST /api/teams
 * @desc    Yeni bir takım oluştur
 * @access  Authenticated
 */
router.post('/', async (req: AuthRequest, res: Response) => {
  try {
    const { name, description, tags, settings } = req.body;
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    logger.info(`Creating new team "${name}" for user ${userId}`);

    const result = await createTeam({
      name,
      description,
      tags,
      settings,
      createdBy: userId
    });

    if (!result.success) {
      logger.error(`Failed to create team: ${result.message}`);
      return res.status(400).json({ error: result.message });
    }



    return res.status(201).json({
      success: true,
      data: {
        teamId: result.teamId
      }
    });
  } catch (error: any) {
    logger.error(`Error in POST /teams: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @route   GET /api/teams
 * @desc    Kullanıcının takımlarını getir
 * @access  Authenticated
 */
router.get('/', async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Sayfalama seçeneklerini al
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
    const skip = req.query.page ? (parseInt(req.query.page as string) - 1) * limit : 0;
    const includeArchived = req.query.includeArchived === 'true';

    logger.info(`Fetching teams for user ${userId}`);

    const result = await getTeamsByUserId(userId, {
      limit,
      skip,
      includeArchived
    });

    if (!result.success) {
      logger.error(`Failed to fetch teams: ${result.message}`);
      return res.status(400).json({ error: result.message });
    }

    return res.status(200).json({
      success: true,
      data: {
        teams: result.teams,
        total: result.total,
        page: skip / limit + 1,
        limit
      }
    });
  } catch (error: any) {
    logger.error(`Error in GET /teams: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @route   GET /api/teams/:teamId
 * @desc    Belirli bir takımın detaylarını getir
 * @access  Team Member
 */
router.get('/:teamId', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId } = req.params;
    const userId = req.user?.id;

    logger.info(`Fetching team ${teamId} for user ${userId}`);

    // Kullanıcının takım üyeliğini kontrol et
    const membershipCheck = await checkTeamMembership(userId!, teamId);

    if (!membershipCheck.success) {
      logger.error(`Membership check error: ${membershipCheck.message}`);
      return res.status(500).json({ error: 'Failed to verify team membership' });
    }

    if (!membershipCheck.isMember) {
      logger.warn(`User ${userId} attempted to access team ${teamId} but is not a member`);
      return res.status(403).json({ error: 'You are not a member of this team' });
    }

    // Takım bilgilerini getir
    const result = await getTeamById(teamId);

    if (!result.success) {
      logger.error(`Failed to fetch team: ${result.message}`);
      return res.status(404).json({ error: result.message });
    }

    return res.status(200).json({
      success: true,
      data: {
        team: result.team
      }
    });
  } catch (error: any) {
    logger.error(`Error in GET /teams/:teamId: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @route   PUT /api/teams/:teamId
 * @desc    Takım bilgilerini güncelle
 * @access  Team Admin
 */
router.put('/:teamId', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId } = req.params;
    const userId = req.user?.id;
    const updateData = req.body;

    logger.info(`Updating team ${teamId} by user ${userId}`);

    // Yetkiyi kontrol et (sadece takım adminleri güncelleyebilir)
    const authCheck = await authorizeTeamAction(userId!, teamId, ['team_admin']);

    if (!authCheck.success) {
      logger.error(`Authorization check error: ${authCheck.message}`);
      return res.status(500).json({ error: 'Failed to verify authorization' });
    }

    if (!authCheck.authorized) {
      logger.warn(`User ${userId} attempted to update team ${teamId} but is not authorized`);
      return res.status(403).json({ error: authCheck.message });
    }

    // Güvenli olmayan alanları kaldır
    delete updateData.id;
    delete updateData.createdAt;
    delete updateData.createdBy;

    // Takımı güncelle
    const result = await updateTeam(teamId, updateData);

    if (!result.success) {
      logger.error(`Failed to update team: ${result.message}`);
      return res.status(400).json({ error: result.message });
    }



    return res.status(200).json({
      success: true,
      data: {
        message: 'Team updated successfully'
      }
    });
  } catch (error: any) {
    logger.error(`Error in PUT /teams/:teamId: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @route   DELETE /api/teams/:teamId
 * @desc    Takımı arşivle (sil)
 * @access  Team Admin
 */
router.delete('/:teamId', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId } = req.params;
    const userId = req.user?.id;

    logger.info(`Archiving team ${teamId} by user ${userId}`);

    // Yetkiyi kontrol et (sadece takım adminleri silebilir)
    const authCheck = await authorizeTeamAction(userId!, teamId, ['team_admin']);

    if (!authCheck.success) {
      logger.error(`Authorization check error: ${authCheck.message}`);
      return res.status(500).json({ error: 'Failed to verify authorization' });
    }

    if (!authCheck.authorized) {
      logger.warn(`User ${userId} attempted to delete team ${teamId} but is not authorized`);
      return res.status(403).json({ error: authCheck.message });
    }

    // Takımı arşivle
    const result = await deleteTeam(teamId);

    if (!result.success) {
      logger.error(`Failed to archive team: ${result.message}`);
      return res.status(400).json({ error: result.message });
    }



    return res.status(200).json({ success: true });
  } catch (error: any) {
    logger.error(`Error in DELETE /teams/:teamId: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @route   POST /api/teams/:teamId/members
 * @desc    Takıma yeni üye ekle
 * @access  Team Admin or Company Owner
 */
router.post('/:teamId/members', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId } = req.params;
    const { userId, roleId } = req.body;
    const addedBy = req.user!.id;
    const accountType = req.user!.accountType;

    logger.info(`Adding user ${userId} to team ${teamId} with role ${roleId} by user ${addedBy} with accountType ${accountType}`);

    // Permission-based kontrol: Team.manage permission'ı gerekli
    const userPermissions = req.user?.permissions || [];
    const hasTeamManagePermission = hasPermission(userPermissions, 'Team', 'manage');

    if (!hasTeamManagePermission) {
      logger.warn(`User ${addedBy} attempted to add member to team ${teamId} but lacks Team.manage permission`);
      return res.status(403).json({
        error: 'You need Team management permission to add members to teams',
        required_permission: { resource: 'Team', action: 'manage' }
      });
    }

    logger.info(`User ${addedBy} has Team.manage permission and can add members to team ${teamId}`);

    // Gerekli alanları kontrol et
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Rol ID'si yoksa varsayılan olarak 'team_member' kullan
    let finalRoleId = roleId;
    if (!finalRoleId) {
      logger.info(`No role ID provided, using default 'team_member' role`);
      finalRoleId = 'team_member';
    }

    // Rol ID'sini logla
    logger.info(`Using role ID: ${finalRoleId} for adding user ${userId} to team ${teamId}`);

    // Rol ID'si büyük harfle başlıyorsa ("Tester" gibi), küçük harfe çevir ("tester")
    if (typeof finalRoleId === 'string' && finalRoleId !== finalRoleId.toLowerCase()) {
      const originalRoleId = finalRoleId;
      finalRoleId = finalRoleId.toLowerCase();
      logger.info(`Converting role ID from ${originalRoleId} to lowercase: ${finalRoleId}`);
    }

    // Üye ekle
    const result = await addTeamMember(teamId, userId, finalRoleId, addedBy);

    if (!result.success) {
      logger.error(`Failed to add team member: ${result.message}`);
      return res.status(400).json({ error: result.message });
    }



    return res.status(201).json({
      success: true,
      data: {
        memberId: result.memberId
      }
    });
  } catch (error: any) {
    logger.error(`Error in POST /teams/:teamId/members: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @route   GET /api/teams/:teamId/members
 * @desc    Takım üyelerini getir
 * @access  Team Member
 */
router.get('/:teamId/members', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId } = req.params;

    // Sayfalama seçeneklerini al
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
    const skip = req.query.page ? (parseInt(req.query.page as string) - 1) * limit : 0;
    const includeInactive = req.query.includeInactive === 'true';

    logger.info(`Fetching members for team ${teamId}`);

    // Sadece admin'ler inaktif üyeleri görebilir
    if (includeInactive) {
      const userId = req.user?.id;
      if (!userId) {
        return res.status(401).json({ error: 'Unauthorized' });
      }
      const authCheck = await authorizeTeamAction(userId, teamId, ['team_admin']);

      if (!authCheck.success) {
        logger.error(`Authorization check error: ${authCheck.message}`);
        return res.status(500).json({ error: 'Failed to verify authorization' });
      }

      if (!authCheck.authorized) {
        logger.warn(`User ${userId} attempted to view inactive members but is not authorized`);
        return res.status(403).json({ error: 'You are not authorized to view inactive members' });
      }
    }

    // Üyeleri getir
    const result = await getTeamMembers(teamId, {
      limit,
      skip,
      includeInactive
    });

    if (!result.success) {
      logger.error(`Failed to fetch team members: ${result.message}`);
      return res.status(400).json({ error: result.message });
    }

    return res.status(200).json({
      success: true,
      data: {
        members: result.members,
        total: result.total,
        page: skip / limit + 1,
        limit
      }
    });
  } catch (error: any) {
    logger.error(`Error in GET /teams/:teamId/members: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @route   PUT /api/teams/:teamId/members/:userId
 * @desc    Takım üyesini güncelle
 * @access  Team Admin or Company Owner
 */
router.put('/:teamId/members/:userId', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId, userId } = req.params;
    const { roleId, status } = req.body;

    if (!req.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const actionBy = req.user.id;
    const accountType = req.user.accountType;

    logger.info(`Updating member ${userId} in team ${teamId} by user ${actionBy} with accountType ${accountType}`);

    // Permission-based kontrol: Team.manage permission'ı gerekli
    const userPermissions = req.user?.permissions || [];
    const hasTeamManagePermission = hasPermission(userPermissions, 'Team', 'manage');

    if (!hasTeamManagePermission) {
      logger.warn(`User ${actionBy} attempted to update team member but lacks Team.manage permission`);
      return res.status(403).json({
        error: 'You need Team management permission to update team members',
        required_permission: { resource: 'Team', action: 'manage' }
      });
    }

    logger.info(`User ${actionBy} has Team.manage permission and can update members in team ${teamId}`);

    // Son admin'i inaktif yapma kontrolü
    if (status === MembershipStatus.INACTIVE) {
      if (userId === actionBy) {
        const teamMembers = await getTeamMembers(teamId);

        if (teamMembers.success && teamMembers.members) {
          const adminMembers = teamMembers.members.filter(
            member => member.roleId === 'team_admin' && member.status === MembershipStatus.ACTIVE
          );

          if (adminMembers.length <= 1) {
            logger.warn(`User ${actionBy} attempted to remove themselves as the last admin of team ${teamId}`);
            return res.status(400).json({
              error: 'Cannot remove the last admin from the team. Assign another admin first.'
            });
          }
        }
      }
    }

    // Rol ID'si varsa işle
    let finalRoleId = roleId;
    if (roleId) {
      // Rol ID'sini logla
      logger.info(`Using role ID: ${finalRoleId} for updating user ${userId} in team ${teamId}`);

      // Rol ID'si büyük harfle başlıyorsa ("Tester" gibi), küçük harfe çevir ("tester")
      if (typeof finalRoleId === 'string' && finalRoleId !== finalRoleId.toLowerCase()) {
        const originalRoleId = finalRoleId;
        finalRoleId = finalRoleId.toLowerCase();
        logger.info(`Converting role ID from ${originalRoleId} to lowercase: ${finalRoleId}`);
      }
    }

    // Üyeyi güncelle
    const result = await updateTeamMember(teamId, userId, {
      roleId: finalRoleId,
      status
    });

    if (!result.success) {
      logger.error(`Failed to update team member: ${result.message}`);
      return res.status(400).json({ error: result.message });
    }



    return res.status(200).json({
      success: true,
      data: {
        message: 'Team member updated successfully'
      }
    });
  } catch (error: any) {
    logger.error(`Error in PUT /teams/:teamId/members/:userId: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @route   DELETE /api/teams/:teamId/members/:userId
 * @desc    Takımdan üye sil
 * @access  Team Admin or CompanyLeader
 */
router.delete('/:teamId/members/:userId', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId, userId } = req.params;
    // Kullanıcı kimliğini doğrula
    const actionBy = req.user?.id;

    if (!actionBy) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    logger.info(`User ${actionBy} attempts to remove member ${userId} from team ${teamId}`);

    // Kullanıcı bilgilerini kontrol et (CompanyLeader, Company Owner veya Admin her üyeyi silebilir)
    const isCompanyLeader = req.user?.role === 'companyLeader';
    const isCompanyOwner = req.user?.accountType === 'company_owner';
    const isAdmin = req.user?.accountType === 'admin';

    // Kendini silme kontrolü
    const isSelfRemoval = userId === actionBy;

    // Company owner kendisini silemez
    if (isCompanyOwner && isSelfRemoval) {
      logger.warn(`Company owner ${actionBy} attempted to remove themselves, which is not allowed`);
      return res.status(403).json({ error: 'Company owners cannot remove themselves' });
    }

    if (!isCompanyLeader && !isCompanyOwner && !isAdmin) {
      // CompanyLeader, Company Owner veya Admin değilse, yetkiyi kontrol et (sadece takım adminleri veya kendisi silebilir)
      if (!isSelfRemoval) {
        const authCheck = await authorizeTeamAction(actionBy, teamId, ['team_admin']);

        if (!authCheck.success) {
          logger.error(`Authorization check error: ${authCheck.message}`);
          return res.status(500).json({ error: 'Failed to verify authorization' });
        }

        if (!authCheck.authorized) {
          logger.warn(`User ${actionBy} attempted to remove member ${userId} but is not authorized`);
          return res.status(403).json({ error: authCheck.message });
        }
      }
    } else if (isCompanyLeader) {
      logger.info(`User ${actionBy} is a CompanyLeader and has permission to remove any member`);
    } else if (isCompanyOwner) {
      logger.info(`User ${actionBy} is a Company Owner and has permission to remove any member`);
    } else if (isAdmin) {
      logger.info(`User ${actionBy} is an Admin and has permission to remove any member`);
    }

    // Son admini silme kontrolü
    const teamMembers = await getTeamMembers(teamId);

    if (teamMembers.success && teamMembers.members) {
      // Check for both userId and user_id fields to handle different formats
      const memberToRemove = teamMembers.members.find(member =>
        member.userId === userId || member.user_id === userId ||
        (member.user && member.user.id === userId)
      );

      if (!memberToRemove) {
        logger.warn(`Member ${userId} not found in team ${teamId}`);
        return res.status(404).json({ error: 'Member not found in team' });
      }

      // Silinen üye admin ise ve son admin ise engelle
      if (memberToRemove.roleId === 'team_admin' || memberToRemove.role_id === 'team_admin') {
        const adminMembers = teamMembers.members.filter(
          member => (member.roleId === 'team_admin' || member.role_id === 'team_admin') &&
                   (member.status === MembershipStatus.ACTIVE || member.status === 'active')
        );

        if (adminMembers.length <= 1) {
          logger.warn(`User ${actionBy} attempted to remove the last admin of team ${teamId}`);
          return res.status(400).json({
            error: 'Cannot remove the last admin of the team. Assign another admin first.'
          });
        }
      }
    }

    // Takımdan üye sil ve kullanıcıyı veritabanından tamamen kaldır
    const result = await removeTeamMember(teamId, userId);

    if (!result.success) {
      logger.error(`Failed to remove team member: ${result.message}`);
      return res.status(400).json({ error: result.message });
    }

    // Kullanıcının teamId ve companyId alanlarını null yap
    if (isMongoDBInitialized() && usersCollection && db) {
      try {
        await usersCollection.updateOne(
          { id: userId },
          { $set: { teamId: null, companyId: null, updatedAt: new Date() } }
        );
        logger.info(`User ${userId} teamId and companyId set to null`);
      } catch (error: any) {
        logger.error(`Error updating user: ${error.message}`);
        // Kullanıcı güncellenemese bile takımdan silindi, bu yüzden hata dönmüyoruz
      }
    } else {
      logger.warn(`MongoDB not initialized, could not update user ${userId}`);
    }



    return res.status(200).json({ success: true });
  } catch (error: any) {
    logger.error(`Error in DELETE /teams/:teamId/members/:userId: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @route   GET /api/teams/:teamId/roles
 * @desc    Takıma ait rolleri listele
 * @access  Team Member
 */
router.get('/:teamId/roles', async (req, res) => {
  try {
    const { teamId } = req.params;
    const includeSystem = req.query.includeSystem !== 'false'; // Varsayılan olarak sistem rollerini dahil et

    logger.info(`Fetching roles for team ${teamId}`);

    // Rolleri getir
    const result = await getRolesByTeamId(teamId, { includeSystem });

    if (!result.success) {
      logger.error(`Failed to fetch roles: ${result.message}`);
      return res.status(400).json({ error: result.message });
    }

    return res.status(200).json({
      success: true,
      data: {
        roles: result.roles
      }
    });
  } catch (error: any) {
    logger.error(`Error in GET /teams/:teamId/roles: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @route   POST /api/teams/:teamId/roles
 * @desc    Takıma özel rol oluştur
 * @access  Team Admin
 */
router.post('/:teamId/roles', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId } = req.params;

    if (!req.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const userId = req.user.id;
    const { name, description, permissions } = req.body;

    logger.info(`Creating new role "${name}" for team ${teamId} by user ${userId}`);

    // Yetkiyi kontrol et (takım adminleri veya company owner rol oluşturabilir)
    const isCompanyOwner = req.user.accountType === 'company_owner';

    if (!isCompanyOwner) {
      const authCheck = await authorizeTeamAction(userId, teamId, ['team_admin']);

      if (!authCheck.success) {
        logger.error(`Authorization check error: ${authCheck.message}`);
        return res.status(500).json({ error: 'Failed to verify authorization' });
      }

      if (!authCheck.authorized) {
        logger.warn(`User ${userId} attempted to create role for team ${teamId} but is not authorized`);
        return res.status(403).json({ error: authCheck.message });
      }
    } else {
      logger.info(`User ${userId} is a company_owner and has permission to create roles for team ${teamId}`);
    }

    // Gerekli alanları kontrol et
    if (!name) {
      return res.status(400).json({ error: 'Role name is required' });
    }

    if (!permissions || !Array.isArray(permissions) || permissions.length === 0) {
      return res.status(400).json({ error: 'At least one permission is required' });
    }

    // Takımın şirket ID'sini bul
    if (!db) {
      logger.error('Database connection not established');
      return res.status(500).json({ error: 'Database connection error' });
    }

    const teamsCollection = db.collection('teams');
    const team = await teamsCollection.findOne({ id: teamId });
    const companyId = team?.companyId;

    logger.info(`Creating role for team ${teamId} with company ID: ${companyId || 'not found'}`);

    // Takıma özel rol oluşturma
    if (!name || !permissions || !Array.isArray(permissions)) {
      return res.status(400).json({ error: 'Name and permissions are required' });
    }

    // Unique rol adı ve ID oluştur
    const generateUniqueRoleData = async (baseName: string, teamId: string): Promise<{roleId: string, roleName: string}> => {
      const { MongoClient } = await import('mongodb');
      const client = new MongoClient(process.env.MONGODB_URI!);
      await client.connect();
      const db = client.db();

      let roleId = baseName.toLowerCase().replace(/\s+/g, '_');
      let roleName = baseName;
      let counter = 0;

      while (true) {
        const currentRoleId = counter === 0 ? roleId : `${roleId}_${counter}`;
        const currentRoleName = counter === 0 ? roleName : `${roleName}_${counter}`;

        // Aynı takımda bu role_id veya role_name var mı kontrol et
        const existingRoleById = await db.collection('role_permissions').findOne({
          role_id: currentRoleId,
          team_id: teamId,
          is_system: false
        });

        const existingRoleByName = await db.collection('role_permissions').findOne({
          role_name: currentRoleName,
          team_id: teamId,
          is_system: false
        });

        if (!existingRoleById && !existingRoleByName) {
          await client.close();
          return { roleId: currentRoleId, roleName: currentRoleName };
        }

        counter++;
      }
    };

    const { roleId: uniqueRoleId, roleName: uniqueRoleName } = await generateUniqueRoleData(name, teamId);

    // Takıma özel rol oluştur
    const { MongoClient } = await import('mongodb');
    const client = new MongoClient(process.env.MONGODB_URI!);
    await client.connect();
    const db = client.db();

    const customRole = {
      id: `custom-${uniqueRoleId}-${teamId}`,
      role_id: uniqueRoleId,
      role_name: uniqueRoleName,
      description: description || `Custom role for team ${teamId}`,
      permissions: permissions,
      team_id: teamId,
      company_id: req.user.companyId,
      created_by: userId,
      created_at: new Date(),
      is_system: false,
      is_custom: true
    };

    await db.collection('role_permissions').insertOne(customRole);
    await client.close();

    logger.info(`[TEAM] Custom role created: ${uniqueRoleId} for team ${teamId} by user ${userId}`);

    return res.status(201).json({
      success: true,
      data: {
        role: {
          id: customRole.role_id,
          name: customRole.role_name,
          description: customRole.description,
          permissions: customRole.permissions,
          isCustom: true
        }
      }
    });
  } catch (error: any) {
    logger.error(`Error in POST /teams/:teamId/roles: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @route   PUT /api/teams/:teamId/roles/:roleId
 * @desc    Rol bilgilerini güncelle
 * @access  Team Admin
 */
router.put('/:teamId/roles/:roleId', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId, roleId } = req.params;

    if (!req.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const userId = req.user.id;
    const { name, description, permissions } = req.body;

    logger.info(`Updating role ${roleId} for team ${teamId} by user ${userId}`);

    // Yetkiyi kontrol et (takım adminleri veya company owner rolleri güncelleyebilir)
    const isCompanyOwner = req.user.accountType === 'company_owner';

    if (!isCompanyOwner) {
      const authCheck = await authorizeTeamAction(userId, teamId, ['team_admin']);

      if (!authCheck.success) {
        logger.error(`Authorization check error: ${authCheck.message}`);
        return res.status(500).json({ error: 'Failed to verify authorization' });
      }

      if (!authCheck.authorized) {
        logger.warn(`User ${userId} attempted to update role for team ${teamId} but is not authorized`);
        return res.status(403).json({ error: authCheck.message });
      }
    } else {
      logger.info(`User ${userId} is a company_owner and has permission to update roles for team ${teamId}`);
    }

    // Sistem rolleri güncellenemez
    return res.status(400).json({
      error: 'System roles cannot be updated. Please use existing system roles: admin, team_admin, developer, viewer'
    });
  } catch (error: any) {
    logger.error(`Error in PUT /teams/:teamId/roles/:roleId: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @route   DELETE /api/teams/:teamId/roles/:roleId
 * @desc    Rol sil
 * @access  Team Admin
 */
router.delete('/:teamId/roles/:roleId', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId, roleId } = req.params;

    if (!req.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const userId = req.user.id;

    logger.info(`Deleting role ${roleId} from team ${teamId} by user ${userId}`);

    // Yetkiyi kontrol et (takım adminleri veya company owner rolleri silebilir)
    const isCompanyOwner = req.user.accountType === 'company_owner';

    if (!isCompanyOwner) {
      const authCheck = await authorizeTeamAction(userId, teamId, ['team_admin']);

      if (!authCheck.success) {
        logger.error(`Authorization check error: ${authCheck.message}`);
        return res.status(500).json({ error: 'Failed to verify authorization' });
      }

      if (!authCheck.authorized) {
        logger.warn(`User ${userId} attempted to delete role from team ${teamId} but is not authorized`);
        return res.status(403).json({ error: authCheck.message });
      }
    } else {
      logger.info(`User ${userId} is a company_owner and has permission to delete roles from team ${teamId}`);
    }

    // Sistem rolleri silinemez
    return res.status(400).json({
      error: 'System roles cannot be deleted. Please use existing system roles: admin, team_admin, developer, viewer'
    });
  } catch (error: any) {
    logger.error(`Error in DELETE /teams/:teamId/roles/:roleId: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @route   POST /api/teams/:teamId/members/bulk-invite
 * @desc    Birden fazla kullanıcıyı takıma davet et
 * @access  Team Admin or Company Owner
 */
router.post('/:teamId/members/bulk-invite', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId } = req.params;

    if (!req.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const userId = req.user.id;
    const accountType = req.user.accountType;
    const { emails, roleId, password } = req.body;

    logger.info(`Bulk inviting users to team ${teamId} by user ${userId} with accountType ${accountType}`);

    // Permission-based kontrol: Team.manage permission'ı gerekli
    const userPermissions = req.user?.permissions || [];
    const hasTeamManagePermission = userPermissions.some((permission: any) =>
      permission.resource === 'Team' &&
      (permission.action === 'manage' || permission.action === '*')
    );

    // Wildcard permissions kontrolü (admin için)
    const hasWildcardPermission = userPermissions.some((permission: any) =>
      permission.resource === '*' &&
      (permission.action === 'manage' || permission.action === '*')
    );

    if (!hasTeamManagePermission && !hasWildcardPermission) {
      logger.warn(`User ${userId} attempted to bulk invite to team ${teamId} but lacks Team.manage permission`);
      return res.status(403).json({
        error: 'You need Team management permission to add members to teams',
        required_permission: { resource: 'Team', action: 'manage' }
      });
    }

    logger.info(`User ${userId} has Team.manage permission and can bulk invite to team ${teamId}`);

    // Gerekli alanları kontrol et
    if (!emails || !Array.isArray(emails) || emails.length === 0) {
      return res.status(400).json({ error: 'At least one email is required' });
    }

    // Rol ID'si yoksa varsayılan olarak 'team_member' kullan
    let finalRoleId = roleId;
    if (!finalRoleId) {
      logger.info(`No role ID provided for bulk invite, using default 'team_member' role`);
      finalRoleId = 'team_member';
    }

    // Rol ID'sini logla
    logger.info(`Using role ID: ${finalRoleId} for bulk inviting users to team ${teamId}`);

    // Rol ID'si büyük harfle başlıyorsa ("Tester" gibi), küçük harfe çevir ("tester")
    if (typeof finalRoleId === 'string' && finalRoleId !== finalRoleId.toLowerCase()) {
      const originalRoleId = finalRoleId;
      finalRoleId = finalRoleId.toLowerCase();
      logger.info(`Converting role ID from ${originalRoleId} to lowercase: ${finalRoleId}`);
    }

    // Her e-posta için davet oluştur
    const results = [];
    for (const email of emails) {
      // E-posta formatını kontrol et (basit validasyon)
      if (!email || typeof email !== 'string' || !email.includes('@')) {
        results.push({
          email,
          success: false,
          message: 'Invalid email format'
        });
        continue;
      }

      try {
        // User service'deki inviteUserToTeam fonksiyonunu kullan
        const inviteResult = await inviteUserToTeam(userId, teamId, email, finalRoleId, password);

        results.push({
          email,
          success: inviteResult.success,
          message: inviteResult.message,
          newUserId: inviteResult.newUserId
        });
      } catch (inviteError: any) {
        results.push({
          email,
          success: false,
          message: inviteError.message || 'Failed to invite user'
        });
      }
    }

    logger.info(`Completed bulk invite for team ${teamId}, sent ${results.filter(r => r.success).length} invitations`);

    return res.status(200).json({
      success: true,
      results
    });
  } catch (error: any) {
    logger.error(`Error in POST /teams/:teamId/members/bulk-invite: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @route   PUT /api/teams/:teamId/members/:userId/status
 * @desc    Takım üye durumunu güncelle (aktif/inaktif)
 * @access  Team Admin or CompanyLeader or Company Owner
 */
router.put('/:teamId/members/:userId/status', async (req: AuthRequest, res: Response) => {
  try {
    const { teamId, userId } = req.params;
    const { status } = req.body;

    if (!req.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const actionBy = req.user.id;
    const accountType = req.user.accountType;

    logger.info(`Updating member ${userId} status to ${status} in team ${teamId} by user ${actionBy} with accountType ${accountType}`);

    // Duplicate log removed;

    if (!status || (status !== MembershipStatus.ACTIVE && status !== MembershipStatus.INACTIVE)) {
      return res.status(400).json({ error: 'Invalid status value. Must be "active" or "inactive"' });
    }

    // Kullanıcı bilgilerini kontrol et (CompanyLeader veya Company Owner her üyenin durumunu değiştirebilir)
    const isCompanyLeader = req.user.role === 'companyLeader';
    const isCompanyOwner = req.user.accountType === 'company_owner';

    // Company owner kendisini deaktif edemez
    if (isCompanyOwner && userId === actionBy) {
      logger.warn(`Company owner ${actionBy} attempted to change their own status, which is not allowed`);
      return res.status(403).json({ error: 'Company owners cannot change their own status' });
    }

    if (!isCompanyLeader && !isCompanyOwner) {
      // CompanyLeader veya Company Owner değilse, admin yetkisi kontrolü yap
      const authCheck = await authorizeTeamAction(actionBy, teamId, ['team_admin']);

      if (!authCheck.success) {
        logger.error(`Authorization check error: ${authCheck.message}`);
        return res.status(500).json({ error: 'Failed to verify authorization' });
      }

      if (!authCheck.authorized) {
        logger.warn(`User ${actionBy} attempted to update team member status but is not authorized`);
        return res.status(403).json({ error: authCheck.message });
      }
    } else if (isCompanyLeader) {
      logger.info(`User ${actionBy} is a CompanyLeader and has permission to change member status`);
    } else if (isCompanyOwner) {
      logger.info(`User ${actionBy} is a Company Owner and has permission to change member status`);
    }

    // Son admin'i inaktif yapma kontrolü
    if (status === MembershipStatus.INACTIVE && userId === actionBy) {
      const teamMembers = await getTeamMembers(teamId);

      if (teamMembers.success && teamMembers.members) {
        const adminMembers = teamMembers.members.filter(
          member => member.roleId === 'team_admin' && member.status === MembershipStatus.ACTIVE
        );

        if (adminMembers.length <= 1) {
          logger.warn(`User ${actionBy} attempted to deactivate themselves as the last admin of team ${teamId}`);
          return res.status(400).json({
            error: 'Cannot deactivate the last admin of the team. Assign another admin first.'
          });
        }
      }
    }

    // Üye durumunu güncelle
    const result = await updateTeamMember(teamId, userId, {
      status
    });

    if (!result.success) {
      logger.error(`Failed to update team member status: ${result.message}`);
      return res.status(400).json({ error: result.message });
    }



    return res.status(200).json({ success: true });
  } catch (error: any) {
    logger.error(`Error in PUT /teams/:teamId/members/:userId/status: ${error.message}`);
    return res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
